#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
project_builder.py - 优化版
单文件多目标/多工具链/多IDE工程导出系统
支持：STM32、HC32、NRF、RISC-V、x86/x64
支持：Keil、VS2022、Eclipse CDT一键导出
零依赖，仅Python3标准库
"""

import os
import sys
import json
import shutil
import argparse
import subprocess
import platform
from pathlib import Path
import xml.etree.ElementTree as ET
from textwrap import dedent

# ------------------------------------------------------------
# 1. 工具链定义
# ------------------------------------------------------------
TOOLCHAINS = {
    "keil": {
        "executable": "armcc.exe",
        "base_cflags": "--c99 -c --cpu {cpu} -D__MICROLIB",
        "ldflags": "--strict --scatter \"{linker_script}\"",
        "macro_prefix": "KEIL_",
        "obj_ext": ".o",
        "bin_ext": ".axf",
        "arch": "arm"
    },
    "gcc": {
        "executable": "arm-none-eabi-gcc",
        "base_cflags": "-Wall -mcpu={cpu} -mthumb -ffunction-sections -fdata-sections",
        "ldflags": "-T \"{linker_script}\" -Wl,--gc-sections",
        "macro_prefix": "GCC_",
        "obj_ext": ".o",
        "bin_ext": ".elf",
        "arch": "arm"
    },
    "armcc": {
        "executable": "armcc.exe",
        "base_cflags": "--cpu {cpu} -DARMCC",
        "ldflags": "--strict --scatter \"{linker_script}\"",
        "macro_prefix": "ARMCC_",
        "obj_ext": ".o",
        "bin_ext": ".axf",
        "arch": "arm"
    },
    "armgcc": {
        "executable": "arm-none-eabi-gcc",
        "base_cflags": "-Wall -mcpu={cpu} -mthumb -ffunction-sections -fdata-sections",
        "ldflags": "-T \"{linker_script}\" -Wl,--gc-sections",
        "macro_prefix": "ARMGCC_",
        "obj_ext": ".o",
        "bin_ext": ".elf",
        "arch": "arm"
    },
    "msvc": {
        "executable": "cl.exe",
        "base_cflags": "/D_CRT_SECURE_NO_WARNINGS /I\"{include_path}\" /DMSVC_BUILD /D{arch}",
        "ldflags": "/OUT:\"{output}\"",
        "macro_prefix": "MSVC_",
        "obj_ext": ".obj",
        "bin_ext": ".exe",
        "arch": "x64"
    },
    "gcc_x64": {
        "executable": "gcc" if platform.system() != "Windows" else "gcc.exe",
        "base_cflags": "-Wall -m64 -DX64_BUILD -D{arch}",
        "ldflags": "-o \"{output}\"",
        "macro_prefix": "GCC_X64_",
        "obj_ext": ".o",
        "bin_ext": ".exe" if platform.system() == "Windows" else ".bin",
        "arch": "x64"
    },
    "clang_x64": {
        "executable": "clang" if platform.system() != "Windows" else "clang.exe",
        "base_cflags": "-Wall -m64 -DX64_BUILD -D{arch}",
        "ldflags": "-o \"{output}\"",
        "macro_prefix": "CLANG_X64_",
        "obj_ext": ".o",
        "bin_ext": ".bin",
        "arch": "x64"
    }
}

# ------------------------------------------------------------
# 2. 构建类型
# ------------------------------------------------------------
BUILD_TYPES = {
    "debug":   {"cflags": "-g -O0 -DDEBUG", "cflags_msvc": "/Zi /Od /DDEBUG", "ldflags": "", "description": "调试版本"},
    "release": {"cflags": "-O2 -DNDEBUG",   "cflags_msvc": "/O2 /DNDEBUG",    "ldflags": "", "description": "发布版本"},
    "minsize": {"cflags": "-Os -DNDEBUG",   "cflags_msvc": "/O1 /DNDEBUG",    "ldflags": "", "description": "最小体积"},
    "perf":    {"cflags": "-O3 -DNDEBUG -D_PERF", "cflags_msvc": "/O2 /DNDEBUG /D_PERF", "ldflags": "", "description": "性能优化"},
    "safety":  {"cflags": "-O2 -DSAFETY_CHECKS -DBOUNDS_CHECK", "cflags_msvc": "/O2 /DSAFETY_CHECKS /DBOUNDS_CHECK", "ldflags": "", "description": "安全检查"}
}

# ------------------------------------------------------------
# 3. 目标定义（含HC32系列）
# ------------------------------------------------------------
TARGETS = {
    "stm32f103": {"cpu": "Cortex-M3", "arch": "ARM_CORTEX_M3", "linker_script": "stm32f103.ld", "toolchains": ["keil", "gcc", "armcc", "armgcc"], "sources": ["soc/stm32f1xx/**/*.c", "soc/stm32f1xx/**/*.S"]},
    "stm32f407": {"cpu": "Cortex-M4", "arch": "ARM_CORTEX_M4", "linker_script": "stm32f407.ld", "toolchains": ["keil", "gcc", "armcc", "armgcc"], "sources": ["soc/stm32f4xx/**/*.c", "soc/stm32f4xx/**/*.S"]},
    "nrf52840":  {"cpu": "Cortex-M4", "arch": "ARM_CORTEX_M4", "linker_script": "nrf52840.ld",  "toolchains": ["keil", "gcc", "armgcc"],             "sources": ["soc/nrf52/**/*.c",      "soc/nrf52/**/*.S"]},
    "hc32f460":  {"cpu": "Cortex-M4", "arch": "ARM_CORTEX_M4", "linker_script": "hc32f460.ld",  "toolchains": ["gcc", "armgcc"],                  "sources": ["soc/hc32xx/**/*.c",     "soc/hc32xx/**/*.S"]},
    "hc32f452":  {"cpu": "Cortex-M4", "arch": "ARM_CORTEX_M4", "linker_script": "hc32f452.ld",  "toolchains": ["gcc", "armgcc"],                  "sources": ["soc/hc32xx/**/*.c",     "soc/hc32xx/**/*.S"]},
    "x64":       {"cpu": "x86-64",    "arch": "X64",           "linker_script": "",              "toolchains": ["msvc", "gcc_x64", "clang_x64"], "sources": ["simulation/**/*.c"]},
    "x86":       {"cpu": "i686",      "arch": "X86",           "linker_script": "",              "toolchains": ["msvc", "gcc_x64", "clang_x64"], "sources": ["simulation/**/*.c"]},
    "riscv":     {"cpu": "rv32imac",  "arch": "RISCV",         "linker_script": "riscv.ld",      "toolchains": ["gcc"],                          "sources": ["soc/riscv/**/*.c",      "soc/riscv/**/*.S"]}
}

# ------------------------------------------------------------
# 4. 工程导出（Keil / VS2022 / Eclipse）
# ------------------------------------------------------------
class ProjectExporter:
    def __init__(self, builder):
        self.builder = builder
        self.builder.load_config()

    def _collect_project_files(self, target, toolchain):
        """收集工程文件（源文件+头文件）"""
        srcs, incs = self.builder.find_source_files(target, toolchain)
        headers = []
        for inc_dir in incs:
            headers.extend(list(Path(inc_dir).rglob("*.h")))
        return srcs, [str(h) for h in headers]

    # ---- Keil µVision5 ----
    def export_keil(self, target):
        srcs, headers = self._collect_project_files(target, "keil")
        uvprojx_tmpl = dedent('''\
            <?xml version="1.0" encoding="UTF-8" standalone="no" ?>
            <Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">
              <Targets>
                <Target>
                  <TargetName>{target}</TargetName>
                  <ToolsetNumber>0x4</ToolsetNumber>
                  <ToolsetName>ARM-ADS</ToolsetName>
                  <uAC6>0</uAC6>
                  <TargetOption>
                    <TargetCommonOption>
                      <Device>{cpu}</Device>
                      <Vendor>ARM</Vendor>
                    </TargetCommonOption>
                    <OutputName>{name}</OutputName>
                    <CreateHexFile>1</CreateHexFile>
                  </TargetOption>
                  <Groups>
                    <Group>
                      <GroupName>Source</GroupName>
                      <Files>{files}</Files>
                    </Group>
                    <Group>
                      <GroupName>Headers</GroupName>
                      <Files>{headers}</Files>
                    </Group>
                  </Groups>
                </Target>
              </Targets>
            </Project>
        ''')
        
        def format_files(file_list):
            return "".join(f"<File><FileName>{Path(f).relative_to(self.builder.base_dir)}</FileName></File>" for f in file_list)
        
        cfg = TARGETS[target]
        xml = uvprojx_tmpl.format(
            target=target,
            name=self.builder.project_name,
            cpu=cfg["cpu"],
            files=format_files(srcs),
            headers=format_files(headers)
        )
        
        out = self.builder.base_dir / f"{self.builder.project_name}_{target}.uvprojx"
        out.write_text(xml, encoding="utf-8")
        print(f"Keil 工程已导出 → {out}")

    # ---- Visual Studio 2022 ----
    def export_vs(self, target):
        srcs, headers = self._collect_project_files(target, "msvc")
        vcxproj_tmpl = dedent('''\
            <?xml version="1.0" encoding="utf-8"?>
            <Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
              <ItemGroup>
                {src_items}
                {header_items}
              </ItemGroup>
              <PropertyGroup>
                <ConfigurationType>Application</ConfigurationType>
                <PlatformToolset>v143</PlatformToolset>
              </PropertyGroup>
            </Project>
        ''')
        
        def format_items(file_list, tag="ClCompile"):
            return "\n".join(f'<{tag} Include="{Path(f).relative_to(self.builder.base_dir)}" />' for f in file_list)
        
        xml = vcxproj_tmpl.format(
            src_items=format_items(srcs),
            header_items=format_items(headers, "ClInclude")
        )
        
        out = self.builder.base_dir / f"{self.builder.project_name}_{target}.vcxproj"
        out.write_text(xml, encoding="utf-8")
        print(f"VS2022 工程已导出 → {out}")

    # ---- Eclipse CDT ----
    def export_eclipse(self, target):
        srcs, headers = self._collect_project_files(target, "gcc")
        cproject_tmpl = dedent('''\
            <?xml version="1.0" encoding="UTF-8" standalone="no"?>
            <?fileVersion 4.0.0?>
            <cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
              <storageModule moduleId="org.eclipse.cdt.core.settings">
                <cconfiguration id="0">
                  <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="0" moduleId="org.eclipse.cdt.core.settings" name="Default">
                    <externalSettings/>
                    <extensions>
                      <extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
                    </extensions>
                  </storageModule>
                  <storageModule moduleId="cdtBuildSystem" version="4.0.0">
                    <configuration buildProperties="" description="" id="0" name="Default" parent="org.eclipse.cdt.build.core.prefbase.cfg">
                      <folderInfo id="0." name="/" resourcePath="">
                        <toolChain id="cdt.managedbuild.toolchain.gnu.base.1" name="GNU" superClass="cdt.managedbuild.toolchain.gnu.base">
                          <targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
                          <builder id="cdt.managedbuild.target.gnu.builder.base" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" superClass="cdt.managedbuild.target.gnu.builder.base"/>
                        </toolChain>
                      </folderInfo>
                    </configuration>
                  </storageModule>
                </cconfiguration>
              </storageModule>
              <linkedResources>
                {links}
              </linkedResources>
            </cproject>
        ''')
        
        links = ""
        for f in srcs + headers:
            abs_path = Path(f).absolute()
            links += f'<link><name>{abs_path.stem}</name><type>1</type><location>{abs_path}</location></link>'
        
        xml = cproject_tmpl.format(links=links)
        out = self.builder.base_dir / f"{self.builder.project_name}_{target}.cproject"
        out.write_text(xml, encoding="utf-8")
        print(f"Eclipse CDT 工程已导出 → {out}（请用 File → Import → Existing Project）")

# ------------------------------------------------------------
# 5. ProjectBuilder 优化版
# ------------------------------------------------------------
class ProjectBuilder:
    def __init__(self, project_name):
        self.project_name = project_name
        self.base_dir = Path(project_name)
        self.config_path = self.base_dir / "project_config.json"
        self.config = {
            "toolchains": list(TOOLCHAINS.keys()),
            "targets": list(TARGETS.keys()),
            "common_src": ["src"],
            "common_inc": ["include"],
            "soc_src": ["soc"],
            "simulation_src": ["simulation"],
            "default_target": "stm32f103",
            "default_toolchain": "gcc",
            "default_build_type": "debug",
            "toolchain_paths": {},
            "custom_cflags": {
                "global": "",
                "gcc": "-std=gnu11",
                "msvc": "/std:c11",
                "clang_x64": "-std=c11"
            },
            "custom_macros": {
                "global": ["USE_FULL_ASSERT", "ENABLE_LOGGING"],
                "debug": ["DEBUG_MODE", "ENABLE_TRACE"],
                "release": ["NDEBUG", "DISABLE_LOGGING"],
                "safety": ["SAFETY_CHECKS", "BOUNDS_CHECKING"]
            },
            "target_specific": {
                "stm32f103": {
                    "cflags": "-mcpu=cortex-m3 -mthumb -mfloat-abi=soft",
                    "macros": ["STM32F1XX", "USE_HAL_DRIVER"]
                },
                "stm32f407": {
                    "cflags": "-mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16",
                    "macros": ["STM32F4XX", "USE_HAL_DRIVER", "HSE_VALUE=8000000"]
                },
                "hc32f460": {
                    "cflags": "-mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16",
                    "macros": ["HC32F460", "HC32F46x", "USE_DDL_DRIVER"]
                },
                "nrf52840": {
                    "cflags": "-mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16",
                    "macros": ["NRF52840_XXAA", "CONFIG_NFCT_PINS_AS_GPIOS"]
                },
                "riscv": {
                    "cflags": "-march=rv32imac -mabi=ilp32",
                    "macros": ["RISCV", "USE_FREERTOS"]
                },
                "x64": {
                    "cflags": "-m64 -msse4.2" if platform.system() != "Windows" else "",
                    "macros": ["SIMULATION", "ENABLE_PROFILING"]
                }
            },
            "build_types": list(BUILD_TYPES.keys()),
            "exclude_files": ["**/README.md", "**/test_*"]
        }
        self.toolchain_cache = {}

    def generate_project_structure(self):
        """生成项目骨架结构"""
        if self.base_dir.exists():
            print(f"错误: 项目 '{self.project_name}' 已存在!")
            return False
        
        print(f"创建项目: {self.project_name}")
        self.base_dir.mkdir()
        
        # 创建公共目录
        for d in set(self.config["common_src"] + self.config["common_inc"]):
            (self.base_dir / d).mkdir(parents=True, exist_ok=True)
            (self.base_dir / d / "README.txt").write_text(f"添加 {d} 文件到此目录")
        
        # 创建SOC目录
        soc_dirs = {
            "stm32f1xx": ["startup", "driver", "hal"],
            "stm32f4xx": ["startup", "driver", "hal"],
            "nrf52": ["startup", "driver", "hal"],
            "riscv": ["startup", "driver", "hal"],
            "hc32xx": ["startup", "driver", "hal"]
        }
        
        for soc, subs in soc_dirs.items():
            soc_path = self.base_dir / "soc" / soc
            for sub in subs:
                (soc_path / sub).mkdir(parents=True, exist_ok=True)
            
            # 创建示例文件
            if soc.startswith("stm32"):
                (soc_path / "startup" / f"startup_{soc}.S").write_text(f"/* {soc} startup */\n")
                (soc_path / "driver" / f"{soc}_uart.c").write_text(f"// {soc} driver\n")
            elif soc == "hc32xx":
                (soc_path / "startup" / "startup_hc32_gcc.S").write_text("/* HC32 GCC startup */\n")
                (soc_path / "startup" / "startup_hc32_keil.S").write_text("/* HC32 Keil startup */\n")
                (soc_path / "driver" / "hc32_uart.c").write_text("// HC32 UART driver\n")
                (soc_path / "hal" / "hc32_hal.c").write_text("// HC32 HAL\n")
        
        # 创建链接脚本目录
        linker_dir = self.base_dir / "linker_scripts"
        linker_dir.mkdir(parents=True, exist_ok=True)
        for target, cfg in TARGETS.items():
            if cfg["linker_script"]:
                (linker_dir / cfg["linker_script"]).write_text(f"/* {target} linker script */\n")
        
        # 创建仿真目录
        sim_dir = self.base_dir / "simulation"
        sim_dir.mkdir(parents=True, exist_ok=True)
        (sim_dir / "x64_sim.c").write_text("// x64 simulation\n")
        
        # 保存配置和构建脚本
        self.save_config()
        (self.base_dir / "build.py").write_text(
            "#!/usr/bin/env python3\n"
            "import sys, project_builder as pb\n"
            "pb.ProjectBuilder('.').build(*(sys.argv[1:4]))\n"
        )
        
        print("项目结构创建完成!")
        return True

    def save_config(self):
        """保存配置文件"""
        with open(self.config_path, "w", encoding="utf-8") as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)

    def load_config(self):
        """加载配置文件"""
        if not self.config_path.exists():
            print("错误: 未找到 project_config.json")
            return False
        with open(self.config_path, encoding="utf-8") as f:
            self.config = json.load(f)
        return True

    def find_toolchain(self, tc):
        """查找工具链路径（带缓存）"""
        if tc in self.toolchain_cache:
            return self.toolchain_cache[tc]
        
        # 优先使用用户配置路径
        user_path = self.config.get("toolchain_paths", {}).get(tc)
        if user_path and Path(user_path).exists():
            self.toolchain_cache[tc] = user_path
            return user_path
        
        # 查找系统路径
        exe = TOOLCHAINS[tc]["executable"]
        found = shutil.which(exe)
        if found:
            self.toolchain_cache[tc] = found
            return found
        
        print(f"错误: 找不到工具链 {tc} ({exe})")
        return None

    def is_excluded(self, path):
        """检查文件是否在排除列表中"""
        rel_path = Path(path).relative_to(self.base_dir)
        for pat in self.config.get("exclude_files", []):
            if rel_path.match(pat):
                return True
        return False

    def _collect_files(self, patterns, file_types=("*.c", "*.cpp", "*.S")):
        """收集指定模式的文件"""
        files = []
        for pattern in patterns:
            for ext in file_types:
                files.extend(self.base_dir.glob(f"{pattern}/{ext}"))
        return [str(f) for f in files if not self.is_excluded(f)]

    def _collect_headers(self, include_dirs):
        """收集头文件"""
        headers = []
        for inc_dir in include_dirs:
            headers.extend(list(Path(inc_dir).rglob("*.h")))
        return [str(h) for h in headers]

    def find_source_files(self, target, toolchain):
        """查找源文件和包含目录"""
        # 公共源文件和包含目录
        src_files = self._collect_files(self.config["common_src"])
        inc_dirs = [str(self.base_dir / d) for d in self.config["common_inc"]]
        
        # 目标特定的源文件
        for pat in TARGETS[target].get("sources", []):
            src_files.extend(self._collect_files([pat]))
        
        # SOC特定的文件
        soc_map = {
            "stm32f103": "stm32f1xx",
            "stm32f407": "stm32f4xx",
            "nrf52840": "nrf52",
            "hc32f460": "hc32xx",
            "hc32f452": "hc32xx",
            "riscv": "riscv"
        }
        
        if target in soc_map:
            soc_path = str(self.base_dir / "soc" / soc_map[target])
            src_files.extend(self._collect_files([soc_path + "/**"]))
            inc_dirs.append(soc_path)
        
        # 仿真文件
        if target in ("x64", "x86"):
            for d in self.config["simulation_src"]:
                sim_path = str(self.base_dir / d)
                src_files.extend(self._collect_files([sim_path + "/**"]))
                inc_dirs.append(sim_path)
        
        return list(set(src_files)), list(set(inc_dirs))

    def _run_command(self, cmd):
        """运行命令并处理输出"""
        print(f"执行: {cmd}")
        try:
            result = subprocess.run(
                cmd, shell=True, check=True,
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                text=True
            )
            if result.stdout.strip():
                print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"命令执行失败:\n{e.stdout}")
            return False

    def _get_common_flags(self, toolchain, target, build_type):
        """获取公共编译标志"""
        tc_cfg = TOOLCHAINS[toolchain]
        tgt_cfg = TARGETS[target]
        
        base_flags = tc_cfg["base_cflags"].format(
            cpu=tgt_cfg["cpu"], 
            arch=tgt_cfg["arch"],
            include_path=";".join(self.config["common_inc"])
        )
        
        bld_flags = BUILD_TYPES[build_type]["cflags" if not toolchain.startswith("msvc") else "cflags_msvc"]
        cus_flags = self.config["custom_cflags"].get("global", "") + " " + self.config["custom_cflags"].get(toolchain, "")
        macros = f"-D{tc_cfg['macro_prefix']}TOOLCHAIN -D{tgt_cfg['arch']} -DTARGET_{target.upper()} -DBUILD_TYPE_{build_type.upper()}"
        macros += " " + " ".join([f"-D{m}" for m in self.config["custom_macros"].get("global", [])])
        macros += " " + " ".join([f"-D{m}" for m in self.config["custom_macros"].get(build_type, [])])
        
        # 添加目标特定的编译选项和宏
        target_specific = self.config.get("target_specific", {}).get(target, {})
        if target_specific:
            # 添加目标特定的编译选项
            if "cflags" in target_specific:
                cus_flags += " " + target_specific["cflags"]
            
            # 添加目标特定的宏定义
            if "macros" in target_specific:
                macros += " " + " ".join([f"-D{m}" for m in target_specific["macros"]])
        
        return f"{base_flags} {bld_flags} {cus_flags} {macros}"

    def _generate_compile_command(self, toolchain, flags, src, obj):
        """生成编译命令"""
        return f'"{self.find_toolchain(toolchain)}" {flags} -c "{src}" -o "{obj}"'

    def _generate_link_command(self, toolchain, objs, output, ldflags):
        """生成链接命令"""
        return f'"{self.find_toolchain(toolchain)}" {" ".join(objs)} {ldflags}'

    def build(self, target, toolchain, build_type="debug"):
        """构建项目"""
        # 验证参数
        if toolchain not in TOOLCHAINS:
            print(f"错误: 不支持工具链 {toolchain}")
            return False
        if target not in TARGETS:
            print(f"错误: 不支持目标 {target}")
            return False
        if build_type not in BUILD_TYPES:
            print(f"错误: 不支持构建类型 {build_type}")
            return False
        if not self.load_config():
            return False
        
        tc_cfg = TOOLCHAINS[toolchain]
        tgt_cfg = TARGETS[target]
        
        # 查找源文件
        src_files, inc_dirs = self.find_source_files(target, toolchain)
        if not src_files:
            print("错误: 未找到源文件")
            return False
        
        # 准备构建目录
        build_dir = self.base_dir / "build" / target / toolchain / build_type
        build_dir.mkdir(parents=True, exist_ok=True)
        
        # 准备编译参数
        common_flags = self._get_common_flags(toolchain, target, build_type)
        inc_flags = " ".join([f'-I"{d}"' for d in inc_dirs])
        cflags = f"{common_flags} {inc_flags}"
        
        # 编译所有源文件
        objs = []
        for src in src_files:
            obj = build_dir / (Path(src).stem + tc_cfg["obj_ext"])
            cmd = self._generate_compile_command(toolchain, cflags, src, obj)
            if not self._run_command(cmd):
                return False
            objs.append(str(obj))
        
        # 链接
        output = build_dir / (self.project_name + tc_cfg["bin_ext"])
        ldflags = tc_cfg["ldflags"].format(
            linker_script=str(self.base_dir / "linker_scripts" / tgt_cfg["linker_script"]) if tgt_cfg["linker_script"] else "",
            output=str(output)
        ) + " " + BUILD_TYPES[build_type].get("ldflags", "")
        
        link_cmd = self._generate_link_command(toolchain, objs, output, ldflags)
        if not self._run_command(link_cmd):
            return False
        
        print(f"\n✅ 构建成功 → {output}")
        return True

    def clean(self, target="all", toolchain="all", build_type="all"):
        """清理构建文件"""
        build_dir = self.base_dir / "build"
        if not build_dir.exists():
            print("无构建文件可清理")
            return
        
        targets = [target] if target != "all" else TARGETS.keys()
        toolchains = [toolchain] if toolchain != "all" else TOOLCHAINS.keys()
        build_types = [build_type] if build_type != "all" else BUILD_TYPES.keys()
        
        cleaned = False
        for t in targets:
            for tc in toolchains:
                for bt in build_types:
                    dir_path = build_dir / t / tc / bt
                    if dir_path.exists():
                        shutil.rmtree(dir_path)
                        print(f"清理: {t}/{tc}/{bt}")
                        cleaned = True
        
        print("清理完成" if cleaned else "无匹配的构建目录")

# ------------------------------------------------------------
# 6. CLI优化版
# ------------------------------------------------------------
def main():
    parser = argparse.ArgumentParser(
        description="多目标多工具链构建系统",
        formatter_class=argparse.RawTextHelpFormatter
    )
    subparsers = parser.add_subparsers(dest="cmd", required=True)
    
    # new 命令
    new_parser = subparsers.add_parser("new", help="创建新项目")
    new_parser.add_argument("project_name", help="项目名称")
    
    # build 命令
    build_parser = subparsers.add_parser("build", help="构建项目")
    build_parser.add_argument("project_dir", help="项目目录")
    build_parser.add_argument("target", nargs="?", default="stm32f103", help="目标平台")
    build_parser.add_argument("toolchain", nargs="?", default="gcc", help="工具链")
    build_parser.add_argument("build_type", nargs="?", default="debug", help="构建类型")
    
    # clean 命令
    clean_parser = subparsers.add_parser("clean", help="清理构建文件")
    clean_parser.add_argument("project_dir", help="项目目录")
    clean_parser.add_argument("target", nargs="?", default="all", help="目标平台 (默认为all)")
    clean_parser.add_argument("toolchain", nargs="?", default="all", help="工具链 (默认为all)")
    clean_parser.add_argument("build_type", nargs="?", default="all", help="构建类型 (默认为all)")
    
    # list 命令
    list_parser = subparsers.add_parser("list", help="列出支持的平台、工具链和构建类型")
    
    # export 命令
    export_parser = subparsers.add_parser("export", help="导出工程")
    export_parser.add_argument("project_dir", help="项目目录")
    export_parser.add_argument("ide", choices=["keil", "vs", "eclipse"], help="IDE类型")
    export_parser.add_argument("target", nargs="?", default=None, help="目标平台")
    
    args = parser.parse_args()
    
    if args.cmd == "new":
        ProjectBuilder(args.project_name).generate_project_structure()
    
    elif args.cmd == "build":
        os.chdir(args.project_dir)
        pb = ProjectBuilder(".")
        pb.build(args.target, args.toolchain, args.build_type)
    
    elif args.cmd == "clean":
        os.chdir(args.project_dir)
        ProjectBuilder(".").clean(args.target, args.toolchain, args.build_type)
    
    elif args.cmd == "list":
        print("目标平台:")
        for target, cfg in TARGETS.items():
            specific_cfg = ProjectBuilder(".").config["target_specific"].get(target, {})
            macros = ", ".join(specific_cfg.get("macros", []))
            flags = specific_cfg.get("cflags", "")[:40] + "..." if specific_cfg.get("cflags", "") else ""
            
            print(f"  {target:12} {cfg['cpu']}")
            print(f"    - 工具链: {', '.join(cfg['toolchains'])}")
            if macros:
                print(f"    - 宏定义: {macros}")
            if flags:
                print(f"    - 编译选项: {flags}")
            print()
        
        print("\n工具链:")
        for tc, cfg in TOOLCHAINS.items():
            print(f"  {tc:10} {cfg['executable']} ({cfg['arch']})")
        
        print("\n构建类型:")
        for bt, cfg in BUILD_TYPES.items():
            print(f"  {bt:10} {cfg['description']}")
    
    elif args.cmd == "export":
        os.chdir(args.project_dir)
        pb = ProjectBuilder(".")
        pb.load_config()  # 确保配置已加载
        exporter = ProjectExporter(pb)
        
        # 确定目标平台
        target = args.target or pb.config["default_target"]
        
        if args.ide == "keil":
            exporter.export_keil(target)
        elif args.ide == "vs":
            exporter.export_vs(target)
        elif args.ide == "eclipse":
            exporter.export_eclipse(target)

if __name__ == "__main__":
    main()