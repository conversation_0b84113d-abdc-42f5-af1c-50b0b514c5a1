{"toolchains": ["keil", "gcc", "armcc", "armgcc", "msvc", "gcc_x64", "clang_x64"], "targets": ["stm32f103", "stm32f407", "nrf52840", "hc32f460", "hc32f452", "x64", "x86", "riscv"], "common_src": ["src"], "common_inc": ["include"], "soc_src": ["soc"], "simulation_src": ["simulation"], "default_target": "stm32f103", "default_toolchain": "gcc", "default_build_type": "debug", "toolchain_paths": {"gcc_x64": "C:\\mingw64\\bin\\gcc.exe"}, "custom_cflags": {"global": "", "gcc": "-std=gnu11", "msvc": "/std:c11", "clang_x64": "-std=c11"}, "custom_macros": {"global": ["USE_FULL_ASSERT", "ENABLE_LOGGING"], "debug": ["DEBUG_MODE", "ENABLE_TRACE"], "release": ["NDEBUG", "DISABLE_LOGGING"], "safety": ["SAFETY_CHECKS", "BOUNDS_CHECKING"]}, "target_specific": {"stm32f103": {"cflags": "-mcpu=cortex-m3 -mthumb -mfloat-abi=soft", "macros": ["STM32F1XX", "USE_HAL_DRIVER"]}, "stm32f407": {"cflags": "-mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16", "macros": ["STM32F4XX", "USE_HAL_DRIVER", "HSE_VALUE=8000000"]}, "hc32f460": {"cflags": "-mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16", "macros": ["HC32F460", "HC32F46x", "USE_DDL_DRIVER"]}, "nrf52840": {"cflags": "-mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16", "macros": ["NRF52840_XXAA", "CONFIG_NFCT_PINS_AS_GPIOS"]}, "riscv": {"cflags": "-march=rv32imac -mabi=ilp32", "macros": ["RISCV", "USE_FREERTOS"]}, "x64": {"cflags": "", "macros": ["SIMULATION", "ENABLE_PROFILING"]}}, "build_types": ["debug", "release", "minsize", "perf", "safety"], "exclude_files": ["**/README.md", "**/test_*"]}